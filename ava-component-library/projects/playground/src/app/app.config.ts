import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { routes } from './app.routes';
import {
  LucideAngularModule,
  User,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
  XCircle,
  CircleCheck,
  CheckCircle2,
  TrendingUp,
  AlignVerticalDistributeStart,
  CircleCheckBig,
  Play,
  MoveLeft,
  File,
  CalendarDays,
  EllipsisVertical,
  SquarePen,
  Wifi,
  Search,
  AlertCircle,
  EyeOff,
  Mail,
  Phone,
  Check,
  X,
  Lock,
  Edit,
  Trash,
  Plus,
  Minus,
  Eye,
  Home,
  Layout,
  ChevronDown,
  ChevronUp,
  Bell,
  Grid,
  Star,
  Leaf,
  CheckCircle,
  AlertTriangle,
  XOctagon,
  Sparkles,
  Slash,
  Feather,
  Globe,
  Send,
  Box,
  Paperclip,
  ArrowLeft,
  ArrowRight,
  PanelRightOpen,
  PanelRightClose,
  CloudUpload,
  ArrowUp,
  LaptopMinimal,
  Link,
  Upload,
  Image,
  MessageSquare,
  Wrench,
  Users,
  Archive,
  Trash2,
  Copy,
  SquarePlay,
  Loader,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Zap,
  Sun,
  Moon,
  Truck,
  CreditCard,
  VolumeX,
  Volume2,
  Circle,
  MoveRight,
  Power,
  Briefcase,
  HelpCircle,
  Folder,
  CheckSquare,
  Calendar,
  MessageCircle,
  FileText,
  ChartNoAxesCombined,
  Settings2,
  Package,
  Book,
  BookOpen,
  BookOpenText,
  BookText,
  Code,
  Clock4,
  Flag,
  Accessibility,
  Contrast,
  Keyboard,
  MousePointer2,
  MousePointerClick,
  Mic,
  Target,
  Type,
  Move,
  Hand,
  Palette,
  Download,
  RefreshCw,
  Tag,
  Smartphone,
  ArrowUpDown,
  Layers,
  ChevronsLeft,
  ChartBar,
  Table,
  Save,
  Maximize2,
  List,
  Pause,
  Unlock,
  RotateCcw,
  Shield,
  Building,
  MapPin,
  Linkedin,
  Twitter,
  Github,
  UserPlus,
  Share2,
  MoveUp,
  MoveDown,
  ListFilter,
  Square,
  MoreHorizontal,
  Navigation,
  ShieldCheck,
  Camera,
  Battery,
  Clock,
  ClipboardList,
  ShoppingCart,
  Headphones,
  UserCheck,
  LayoutDashboard,
  Hammer,
  Bot,
  GitBranch,
  Plug,
  Menu,
  Printer,
  Chrome,
  Facebook,
  LogIn,
  WandSparkles,
  Timer,
  AlignCenterHorizontal,
  AlignCenterVertical,
  AlignLeft,
  AlignRight,
  AlignCenter,
  HardDrive,
  FileIcon,
  FileCode,
  Laptop,
  Monitor,
  ListFilterPlus,
} from 'lucide-angular';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimations(),
    importProvidersFrom(
      LucideAngularModule.pick({
        User,
        Settings,
        Info,
        ChevronLeft,
        ChevronRight,
        ArrowRight,
        XCircle,
        CircleCheck,
        AlignVerticalDistributeStart,
        CircleCheckBig,
        MoveLeft,
        Play,
        CalendarDays,
        EllipsisVertical,
        CheckCircle2,
        SquarePen,
        Wifi,
        Search,
        AlertCircle,
        EyeOff,
        Mail,
        Phone,
        Check,
        X,
        Lock,
        Edit,
        Trash,
        Plus,
        ArrowLeft,
        Minus,
        ChevronDown,
        ChevronUp,
        Eye,
        File,
        Home,
        Layout,
        Bell,
        Grid,
        Star,
        Leaf,
        CheckCircle,
        AlertTriangle,
        XOctagon,
        Sparkles,
        Slash,
        Feather,
        Globe,
        Send,
        Box,
        Paperclip,
        PanelRightClose,
        PanelRightOpen,
        TrendingUp,
        CloudUpload,
        ArrowUp,
        LaptopMinimal,
        Link,
        ListFilterPlus,
        Upload,
        Image,
        MessageSquare,
        Wrench,
        Users,
        Archive,
        Trash2,
        Copy,
        SquarePlay,
        Loader,
        Heart,
        ThumbsUp,
        ThumbsDown,
        Zap,
        Sun,
        Moon,
        Truck,
        CreditCard,
        VolumeX,
        Volume2,
        Circle,
        MoveRight,
        Power,
        Briefcase,
        HelpCircle,
        Folder,
        CheckSquare,
        Calendar,
        MessageCircle,
        FileText,
        ChartNoAxesCombined,
        Settings2,
        Package,
        Book,
        BookOpen,
        BookOpenText,
        BookText,
        Code,
        Clock4,
        Flag,
        Accessibility,
        Contrast,
        Keyboard,
        MousePointer2,
        MousePointerClick,
        Mic,
        Target,
        Type,
        Move,
        Hand,
        Palette,
        Download,
        RefreshCw,
        Tag,
        Smartphone,
        ArrowUpDown,
        Layers,
        ChevronsLeft,
        ChartBar,
        Table,
        Save,
        Maximize2,
        List,
        Pause,
        Unlock,
        RotateCcw,
        Shield,
        Building,
        MapPin,
        Linkedin,
        Twitter,
        Github,
        UserPlus,
        Share2,
        MoveUp,
        MoveDown,
        ListFilter,
        Square,
        MoreHorizontal,
        Navigation,
        ShieldCheck,
        Camera,
        Battery,
        Clock,
        ClipboardList,
        ShoppingCart,
        Headphones,
        UserCheck,
        LayoutDashboard,
        Hammer,
        Bot,
        GitBranch,
        Plug,
        Menu,
        Printer,
        Chrome,
        Facebook,
        LogIn,
        WandSparkles,
        Timer,
        AlignCenterHorizontal,
        AlignCenterVertical,
        AlignLeft,
        AlignRight,
        HardDrive,
        AlignCenter,
        FileIcon,
        FileCode,
        Laptop,
        Monitor,
      })
    ),
  ],
};
