import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  JournalDataGridComponent, 
  JournalColumn, 
  JournalDataGridEvent 
} from 'play-comp-library';

@Component({
  selector: 'app-journal-data-grid',
  standalone: true,
  imports: [CommonModule, JournalDataGridComponent],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent {
  
  // Sample journal data matching the image provided
  journalData = [
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    }
  ];

  // Column configuration matching the image
  columns: JournalColumn[] = [
    {
      id: 'journalId',
      label: 'Journal ID',
      width: '120px',
      align: 'left'
    },
    {
      id: 'date',
      label: 'Date',
      width: '120px',
      align: 'left'
    },
    {
      id: 'journalDescription',
      label: 'Journal Description',
      width: '300px',
      align: 'left'
    },
    {
      id: 'journalStatus',
      label: 'Journal Status',
      width: '150px',
      align: 'center'
    },
    {
      id: 'sourceTransaction',
      label: 'Source Transaction',
      width: '150px',
      align: 'left'
    },
    {
      id: 'drCrTotals',
      label: 'Dr/Cr Totals',
      width: '120px',
      align: 'right'
    },
    {
      id: 'documents',
      label: 'Documents',
      width: '100px',
      align: 'center'
    }
  ];

  // Component state
  loading = false;
  selectedRows: Record<string, unknown>[] = [];

  // Event handlers
  onSearchChange(searchTerm: string) {
    console.log('Search changed:', searchTerm);
  }

  onFilterClick() {
    console.log('Filter button clicked');
    // Implement filter logic here
  }

  onSortClick() {
    console.log('Sort button clicked');
    // Implement sort logic here
  }

  onCreateClick() {
    console.log('Create Journal Entry button clicked');
    // Implement create logic here
  }

  onSelectionChange(selectedRows: Record<string, unknown>[]) {
    this.selectedRows = selectedRows;
    console.log('Selection changed:', selectedRows);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    console.log('Journal ID clicked:', row);
    // Implement navigation to journal detail
  }

  onDataGridEvent(event: JournalDataGridEvent) {
    console.log('Data grid event:', event);
    // Handle all data grid events
  }

  // Demo methods for playground
  toggleLoading() {
    this.loading = !this.loading;
  }

  clearSelection() {
    this.selectedRows = [];
  }

  addSampleData() {
    const newEntry = {
      journalId: '388237',
      date: 'mm/dd/yyyy',
      journalDescription: 'New journal entry',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 150000',
      documents: true
    };
    this.journalData = [...this.journalData, newEntry];
  }

  // Usage example for documentation
  usageExample = `<ava-journal-data-grid
  [data]="journalData"
  [columns]="columns"
  [loading]="loading"
  searchPlaceholder="Search for Journal Entries"
  (searchChange)="onSearchChange($event)"
  (filterClick)="onFilterClick()"
  (sortClick)="onSortClick()"
  (createClick)="onCreateClick()"
  (selectionChange)="onSelectionChange($event)"
  (journalIdClick)="onJournalIdClick($event)"
>
</ava-journal-data-grid>`;
}
