import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  JournalDataGridComponent,
  JournalColumn,
  JournalDataGridEvent,
  AvaTextboxComponent,
  ButtonComponent
} from 'play-comp-library';

@Component({
  selector: 'app-journal-data-grid',
  standalone: true,
  imports: [CommonModule, JournalDataGridComponent, AvaTextboxComponent, ButtonComponent],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent {
  
  // Sample journal data matching the image provided
  journalData = [
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    }
  ];

  // Column configuration matching the image
  columns: JournalColumn[] = [
    {
      id: 'journalId',
      label: 'Journal ID',
      width: '120px',
      align: 'left'
    },
    {
      id: 'date',
      label: 'Date',
      width: '120px',
      align: 'left'
    },
    {
      id: 'journalDescription',
      label: 'Journal Description',
      width: '300px',
      align: 'left'
    },
    {
      id: 'journalStatus',
      label: 'Journal Status',
      width: '150px',
      align: 'center'
    },
    {
      id: 'sourceTransaction',
      label: 'Source Transaction',
      width: '150px',
      align: 'left'
    },
    {
      id: 'drCrTotals',
      label: 'Dr/Cr Totals',
      width: '120px',
      align: 'right'
    },
    {
      id: 'documents',
      label: 'Documents',
      width: '100px',
      align: 'center'
    }
  ];

  // Component state
  loading = false;
  selectedRows: Record<string, unknown>[] = [];
  searchTerm = '';
  showFilters = false;

  // Computed property for filtered data
  get filteredData(): Record<string, unknown>[] {
    let filtered = [...this.journalData];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchLower)
        )
      );
    }

    return filtered;
  }

  // Event handlers
  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target?.value || '';
    console.log('Search changed:', this.searchTerm);
  }

  onFilterClick() {
    this.showFilters = !this.showFilters;
    console.log('Filter button clicked, showing filters:', this.showFilters);
  }

  onSortClick() {
    console.log('Sort button clicked');
    // Implement sort logic here
  }

  onCreateClick() {
    console.log('Create Journal Entry button clicked');
    // Implement create logic here
  }

  onSelectionChange(selectedRows: Record<string, unknown>[]) {
    this.selectedRows = selectedRows;
    console.log('Selection changed:', selectedRows);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    console.log('Journal ID clicked:', row);
    // Implement navigation to journal detail
  }

  onDataGridEvent(event: JournalDataGridEvent) {
    console.log('Data grid event:', event);
    // Handle all data grid events
  }

  // Demo methods for playground
  toggleLoading() {
    this.loading = !this.loading;
  }

  clearSelection() {
    this.selectedRows = [];
  }

  addSampleData() {
    const newEntry = {
      journalId: '388237',
      date: 'mm/dd/yyyy',
      journalDescription: 'New journal entry',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 150000',
      documents: true
    };
    this.journalData = [...this.journalData, newEntry];
  }

  // Usage example for documentation
  usageExample = `<!-- Parent component handles header controls -->
<div class="journal-data-grid-header">
  <div class="header-left">
    <ava-textbox
      placeholder="Search for Journal Entries"
      icon="search"
      iconPosition="start"
      (textboxChange)="onSearchChange($event)"
    ></ava-textbox>
  </div>

  <div class="header-center">
    <ava-button label="Filter" variant="secondary"
                iconName="settings" (userClick)="onFilterClick()">
    </ava-button>
    <ava-button label="Sort" variant="secondary"
                iconName="arrow-up-down" (userClick)="onSortClick()">
    </ava-button>
  </div>

  <div class="header-right">
    <ava-button label="Create Journal Entry" variant="primary"
                iconName="plus" (userClick)="onCreateClick()">
    </ava-button>
  </div>
</div>

<!-- Child component handles data display -->
<ava-journal-data-grid
  [data]="journalData"
  [columns]="columns"
  [filteredData]="filteredData"
  [loading]="loading"
  (selectionChange)="onSelectionChange($event)"
  (journalIdClick)="onJournalIdClick($event)"
>
</ava-journal-data-grid>`;
}
