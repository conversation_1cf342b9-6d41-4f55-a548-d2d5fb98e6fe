/* Journal Data Grid Playground Styles */

.demo-page {
  min-height: 100vh;
  background: var(--color-background-primary);
}

.demo-header {
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border-subtle);
  padding: 3rem 0 2rem;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: 1.125rem;
    color: var(--color-text-secondary);
    margin: 0;
    line-height: 1.625;
  }
}

.demo-controls {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-subtle);
  padding: 2rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: var(--color-text-primary);
  }

  .controls-grid {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .demo-btn {
    padding: 0.5rem 1rem;
    background: var(--color-brand-primary);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-brand-primary-hover);
    }

    &:active {
      transform: translateY(1px);
    }
  }
}

.demo-content {
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--color-text-primary);
  }

  p {
    color: var(--color-text-secondary);
    margin: 0 0 2rem 0;
    line-height: 1.5;
  }

  .journal-grid-demo {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    /* Header Controls Styling */
    .journal-data-grid-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background: var(--color-background-secondary);
      border-bottom: 1px solid var(--color-border-subtle);
      gap: 1rem;

      .header-left {
        flex: 1;
        max-width: 400px;

        .search-input {
          width: 100%;
        }
      }

      .header-center {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }

      .header-right {
        display: flex;
        align-items: center;
      }
    }

    /* Filter Panel */
    .filter-panel {
      padding: 1rem 1.5rem;
      background: var(--color-background-primary);
      border-bottom: 1px solid var(--color-border-subtle);
      color: var(--color-text-secondary);
    }

    /* Responsive Header */
    @media (max-width: 768px) {
      .journal-data-grid-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .header-left {
          max-width: 100%;
        }

        .header-center {
          justify-content: center;
        }

        .header-right {
          justify-content: center;
        }
      }
    }
  }
}

.demo-features {
  background: var(--color-background-secondary);
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 2rem 0;
    color: var(--color-text-primary);
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .feature-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
      color: var(--color-text-primary);
    }

    p {
      color: var(--color-text-secondary);
      margin: 0;
      line-height: 1.5;
    }
  }
}

.demo-usage {
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: var(--color-text-primary);
  }

  pre {
    background: var(--color-background-secondary);
    border: 1px solid var(--color-border-subtle);
    border-radius: 6px;
    padding: 1.5rem;
    overflow-x: auto;
    margin: 0;

    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      color: var(--color-text-primary);
    }
  }
}

.demo-api {
  background: var(--color-background-secondary);
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h2, h3 {
    color: var(--color-text-primary);
    margin: 0 0 1rem 0;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 2rem;
  }

  .api-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;

    th, td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--color-border-subtle);
    }

    th {
      background: var(--color-background-disabled);
      font-weight: 600;
      color: var(--color-text-primary);
    }

    td {
      color: var(--color-text-secondary);
      font-size: 0.875rem;

      &:first-child {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-weight: 500;
        color: var(--color-text-primary);
      }
    }

    tr:last-child {
      th, td {
        border-bottom: none;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .demo-header {
    padding: 2rem 0 1.5rem;

    h1 {
      font-size: 2rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .demo-controls {
    .controls-grid {
      flex-direction: column;
    }

    .demo-btn {
      width: 100%;
      text-align: center;
    }
  }

  .demo-content,
  .demo-features,
  .demo-usage,
  .demo-api {
    padding: 2rem 0;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .api-table {
    font-size: 0.75rem;

    th, td {
      padding: 0.5rem;
    }
  }
}
