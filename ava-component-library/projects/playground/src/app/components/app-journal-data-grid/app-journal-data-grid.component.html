<div class="demo-page">
  <!-- Demo Header -->
  <div class="demo-header">
    <div class="container">
      <h1>Journal Data Grid</h1>
      <p>A composite component for displaying journal entries with search, filter, sort, and create functionality.</p>
    </div>
  </div>

  <!-- Demo Controls -->
  <div class="demo-controls">
    <div class="container">
      <h2>Demo Controls</h2>
      <div class="controls-grid">
        <button (click)="toggleLoading()" class="demo-btn">
          {{ loading ? 'Stop Loading' : 'Show Loading' }}
        </button>
        <button (click)="clearSelection()" class="demo-btn">
          Clear Selection ({{ selectedRows.length }} selected)
        </button>
        <button (click)="addSampleData()" class="demo-btn">
          Add Sample Entry
        </button>
      </div>
    </div>
  </div>

  <!-- Main Demo -->
  <div class="demo-content">
    <div class="container">
      <h2>Journal Data Grid Example</h2>
      <p>This example shows the journal data grid with sample data matching the provided design.</p>
      
      <!-- Journal Data Grid Component -->
      <div class="journal-grid-demo">
        <ava-journal-data-grid
          [data]="journalData"
          [columns]="columns"
          [loading]="loading"
          [disabled]="false"
          searchPlaceholder="Search for Journal Entries"
          emptyMessage="No journal entries found"
          loadingMessage="Loading journal entries..."
          (searchChange)="onSearchChange($event)"
          (filterClick)="onFilterClick()"
          (sortClick)="onSortClick()"
          (createClick)="onCreateClick()"
          (selectionChange)="onSelectionChange($event)"
          (journalIdClick)="onJournalIdClick($event)"
          (dataGridEvent)="onDataGridEvent($event)"
        >
        </ava-journal-data-grid>
      </div>
    </div>
  </div>

  <!-- Features Section -->
  <div class="demo-features">
    <div class="container">
      <h2>Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>🔍 Search</h3>
          <p>Search across all journal entry fields with real-time filtering.</p>
        </div>
        <div class="feature-card">
          <h3>🎛️ Filter & Sort</h3>
          <p>Filter and sort buttons for advanced data manipulation.</p>
        </div>
        <div class="feature-card">
          <h3>✅ Selection</h3>
          <p>Multi-select functionality with checkbox support.</p>
        </div>
        <div class="feature-card">
          <h3>🏷️ Status Tags</h3>
          <p>Outlined status tags with color coding for different journal states.</p>
        </div>
        <div class="feature-card">
          <h3>📄 Document Icons</h3>
          <p>Document icons using Lucide Angular icons.</p>
        </div>
        <div class="feature-card">
          <h3>🔗 Clickable IDs</h3>
          <p>Journal IDs are clickable links for navigation.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Usage Example -->
  <div class="demo-usage">
    <div class="container">
      <h2>Usage Example</h2>
      <pre><code>{{usageExample}}</code></pre>
    </div>
  </div>

  <!-- API Documentation -->
  <div class="demo-api">
    <div class="container">
      <h2>API Documentation</h2>
      
      <h3>Inputs</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>data</td>
            <td>Record&lt;string, unknown&gt;[]</td>
            <td>[]</td>
            <td>Array of journal entry data</td>
          </tr>
          <tr>
            <td>columns</td>
            <td>JournalColumn[]</td>
            <td>[]</td>
            <td>Column configuration array</td>
          </tr>
          <tr>
            <td>loading</td>
            <td>boolean</td>
            <td>false</td>
            <td>Shows loading state</td>
          </tr>
          <tr>
            <td>disabled</td>
            <td>boolean</td>
            <td>false</td>
            <td>Disables all interactions</td>
          </tr>
          <tr>
            <td>searchPlaceholder</td>
            <td>string</td>
            <td>'Search for Journal Entries'</td>
            <td>Search input placeholder text</td>
          </tr>
        </tbody>
      </table>

      <h3>Outputs</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>Event</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>searchChange</td>
            <td>string</td>
            <td>Emitted when search term changes</td>
          </tr>
          <tr>
            <td>filterClick</td>
            <td>void</td>
            <td>Emitted when filter button is clicked</td>
          </tr>
          <tr>
            <td>sortClick</td>
            <td>void</td>
            <td>Emitted when sort button is clicked</td>
          </tr>
          <tr>
            <td>createClick</td>
            <td>void</td>
            <td>Emitted when create button is clicked</td>
          </tr>
          <tr>
            <td>selectionChange</td>
            <td>Record&lt;string, unknown&gt;[]</td>
            <td>Emitted when row selection changes</td>
          </tr>
          <tr>
            <td>journalIdClick</td>
            <td>Record&lt;string, unknown&gt;</td>
            <td>Emitted when journal ID is clicked</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
