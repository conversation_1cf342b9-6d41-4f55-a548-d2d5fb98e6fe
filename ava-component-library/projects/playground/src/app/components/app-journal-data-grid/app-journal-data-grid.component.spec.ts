import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AppJournalDataGridComponent } from './app-journal-data-grid.component';

describe('AppJournalDataGridComponent', () => {
  let component: AppJournalDataGridComponent;
  let fixture: ComponentFixture<AppJournalDataGridComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppJournalDataGridComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AppJournalDataGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have sample journal data', () => {
    expect(component.journalData.length).toBeGreaterThan(0);
  });

  it('should have correct column configuration', () => {
    expect(component.columns.length).toBe(7);
    expect(component.columns[0].id).toBe('journalId');
    expect(component.columns[3].id).toBe('journalStatus');
  });

  it('should toggle loading state', () => {
    const initialState = component.loading;
    component.toggleLoading();
    expect(component.loading).toBe(!initialState);
  });

  it('should clear selection', () => {
    component.selectedRows = [{ id: 1 }, { id: 2 }];
    component.clearSelection();
    expect(component.selectedRows.length).toBe(0);
  });

  it('should add sample data', () => {
    const initialLength = component.journalData.length;
    component.addSampleData();
    expect(component.journalData.length).toBe(initialLength + 1);
  });
});
