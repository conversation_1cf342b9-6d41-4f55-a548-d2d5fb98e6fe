<!-- Journal Data Grid Content - Header is now in parent component -->
<div class="journal-data-grid-content">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-overlay">
    <div class="loading-content">
      <ava-icon
        iconName="loader"
        [iconSize]="24"
        iconColor="var(--grid-filter-active-color)"
      ></ava-icon>
      <span>{{ loadingMessage }}</span>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && filteredData.length === 0" class="empty-state">
    <ava-icon iconName="inbox" [iconSize]="48" iconColor="var(--grid-text-disabled)"></ava-icon>
    <p>{{ emptyMessage }}</p>
  </div>

  <!-- Data Table -->
  <div *ngIf="!loading && filteredData.length > 0" class="table-container">
    <table class="journal-data-table">
      <thead>
        <tr>
          <!-- Checkbox Column -->
          <th class="checkbox-column">
            <ava-checkbox
              [isChecked]="isAllSelected"
              [indeterminate]="isIndeterminate"
              [disable]="disabled"
              (isCheckedChange)="onSelectAllChange($event)"
            >
            </ava-checkbox>
          </th>
          
          <!-- Data Columns -->
          <th *ngFor="let column of columns" 
              class="data-column"
              [style.width]="column.width"
              [style.text-align]="column.align || 'left'">
            <span class="column-label">{{ column.label }}</span>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of filteredData; trackBy: trackByRow"
            class="table-row"
            [class.selected]="isRowSelected(row)">
          
          <!-- Checkbox Cell -->
          <td class="checkbox-cell">
            <ava-checkbox
              [isChecked]="isRowSelected(row)"
              [disable]="disabled"
              (isCheckedChange)="onRowSelectionChange(row, $event)"
            >
            </ava-checkbox>
          </td>
          
          <!-- Data Cells -->
          <td *ngFor="let column of columns" 
              class="data-cell"
              [style.text-align]="column.align || 'left'">
            
            <!-- Journal Status Column with Tags -->
            <ava-tag
              *ngIf="column.id === 'journalStatus'"
              [label]="getString(row[column.id])"
              [color]="getStatusColor(getString(row[column.id]).toLowerCase())"
              variant="outlined"
              size="sm"
            ></ava-tag>
            
            <!-- Documents Column with Icon -->
            <div *ngIf="column.id === 'documents'" class="documents-cell">
              <ava-icon
                iconName="file-text"
                [iconSize]="16"
                iconColor="var(--grid-text-color)"
              ></ava-icon>
            </div>
            
            <!-- Journal ID Column with Link Style -->
            <span *ngIf="column.id === 'journalId'" 
                  class="journal-id-link"
                  (click)="onJournalIdClick(row)"
                  (keydown.enter)="onJournalIdClick(row)"
                  tabindex="0"
                  role="button">
              {{ getCellValue(row, column) }}
            </span>
            
            <!-- Regular Columns -->
            <span *ngIf="!isSpecialColumn(column.id)">
              {{ getCellValue(row, column) }}
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
