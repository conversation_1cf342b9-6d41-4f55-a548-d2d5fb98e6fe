import { ComponentFixture, TestBed } from '@angular/core/testing';
import { JournalDataGridComponent } from './journal-data-grid.component';

describe('JournalDataGridComponent', () => {
  let component: JournalDataGridComponent;
  let fixture: ComponentFixture<JournalDataGridComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [JournalDataGridComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(JournalDataGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit search change event', () => {
    spyOn(component.searchChange, 'emit');
    const mockEvent = { target: { value: 'test search' } } as any;
    
    component.onSearchChange(mockEvent);
    
    expect(component.searchChange.emit).toHaveBeenCalledWith('test search');
  });

  it('should handle row selection', () => {
    const mockRow = { id: 1, name: 'Test' };
    spyOn(component.selectionChange, 'emit');
    
    component.onRowSelectionChange(mockRow, true);
    
    expect(component.selectedRows.has(mockRow)).toBeTruthy();
    expect(component.selectionChange.emit).toHaveBeenCalled();
  });

  it('should handle select all', () => {
    component.data = [{ id: 1 }, { id: 2 }];
    spyOn(component.selectionChange, 'emit');
    
    component.onSelectAllChange(true);
    
    expect(component.selectedRows.size).toBe(2);
    expect(component.selectionChange.emit).toHaveBeenCalled();
  });

  it('should filter data based on search term', () => {
    component.data = [
      { journalId: '123', description: 'Test entry' },
      { journalId: '456', description: 'Another entry' }
    ];
    component.searchTerm = 'Test';
    
    const filtered = component.filteredData;
    
    expect(filtered.length).toBe(1);
    expect(filtered[0].description).toBe('Test entry');
  });

  it('should return correct status color', () => {
    expect(component.getStatusColor('posted')).toBe('success');
    expect(component.getStatusColor('template')).toBe('warning');
    expect(component.getStatusColor('rejected')).toBe('error');
    expect(component.getStatusColor('ready to approve')).toBe('info');
    expect(component.getStatusColor('draft')).toBe('default');
  });

  it('should identify special columns', () => {
    expect(component.isSpecialColumn('journalStatus')).toBeTruthy();
    expect(component.isSpecialColumn('documents')).toBeTruthy();
    expect(component.isSpecialColumn('journalId')).toBeTruthy();
    expect(component.isSpecialColumn('regularColumn')).toBeFalsy();
  });
});
