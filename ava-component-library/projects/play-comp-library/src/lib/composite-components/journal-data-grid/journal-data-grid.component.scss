/* Journal Data Grid Component Styles - Uses only _data-grid.css tokens */
/* Header is now handled by parent component */

.journal-data-grid-content {
  position: relative;
  min-height: 200px;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      color: var(--grid-text-color);
      font-family: var(--grid-font-family-body);

      ava-icon {
        animation: spin 1s linear infinite;
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--grid-text-disabled);
    text-align: center;
    font-family: var(--grid-font-family-body);

    p {
      margin: 1rem 0 0 0;
      font-size: 1rem;
    }
  }

  .table-container {
    border: 1px solid var(--grid-border);
    border-radius: 8px;
    overflow-x: auto;

    .journal-data-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      background-color: var(--grid-background-color-odd);
      font-family: var(--grid-font-family-body);
      color: var(--grid-text-color);

      th,
      td {
        padding: var(--grid-cell-paading);
        border-bottom: 1px solid var(--grid-border);
        text-align: left;
        vertical-align: middle;
      }

      thead {
        th {
          background: var(--grid-background-color-even);
          font-weight: 600;
          color: var(--grid-text-color);
          position: sticky;
          top: 0;
          z-index: 5;

          &.checkbox-column {
            width: 50px;
            text-align: center;
          }

          &.data-column {
            .column-label {
              font-weight: 600;
            }
          }
        }
      }

      tbody {
        tr {
          transition: background-color 0.2s ease;

          &:hover {
            background-color: var(--grid-background-color-even);
          }

          &.selected {
            background-color: rgba(var(--grid-filter-active-color), 0.1);
          }

          &:nth-child(even) {
            background-color: var(--grid-background-color-even);
          }

          td {
            &.checkbox-cell {
              text-align: center;
              width: 50px;
            }

            &.data-cell {
              vertical-align: middle;

              .documents-cell {
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .journal-id-link {
                color: var(--grid-filter-active-color);
                cursor: pointer;
                text-decoration: none;
                font-weight: 500;
                transition: color 0.2s ease;

                &:hover {
                  text-decoration: underline;
                  opacity: 0.8;
                }

                &:focus {
                  outline: 2px solid var(--grid-filter-active-color);
                  outline-offset: 2px;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .journal-data-grid-content {
    .table-container {
      .journal-data-table {
        th,
        td {
          padding: calc(var(--grid-cell-paading) * 0.75);
          font-size: 0.875rem;
        }
      }
    }
  }
}

/* Loading Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Disabled State */
.journal-data-grid-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* CSS Comments for Customization */
/*
  Customization Guide:
  
  1. Header Layout:
     - Modify .journal-data-grid-header for header styling
     - Adjust .header-left, .header-center, .header-right for layout
  
  2. Table Styling:
     - Use .journal-data-table for table-wide styles
     - Modify .data-cell for cell-specific styling
     - Adjust .journal-id-link for link appearance
  
  3. Status Tags:
     - Status colors are controlled by getStatusColor() method
     - Tags use outlined variant as specified
  
  4. Responsive Behavior:
     - Mobile styles defined in @media (max-width: 768px)
     - Header stacks vertically on mobile
  
  5. Color Customization:
     - All colors use _data-grid.css tokens
     - Override CSS custom properties to change colors
*/
