import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../components/icon/icon.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { AvaTagComponent } from '../../components/tags/tags.component';

export interface JournalColumn {
  id: string;
  label: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, row: Record<string, unknown>) => string;
}

export interface JournalDataGridEvent {
  type: 'selection-change' | 'journal-id-click';
  data?: unknown;
  selectedRows?: Record<string, unknown>[];
  row?: Record<string, unknown>;
}

@Component({
  selector: 'ava-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    CheckboxComponent,
    AvaTagComponent,
  ],
  templateUrl: './journal-data-grid.component.html',
  styleUrl: './journal-data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class JournalDataGridComponent {
  @Input() data: Record<string, unknown>[] = [];
  @Input() columns: JournalColumn[] = [];
  @Input() loading = false;
  @Input() disabled = false;
  @Input() emptyMessage = 'No journal entries found';
  @Input() loadingMessage = 'Loading journal entries...';
  @Input() filteredData: Record<string, unknown>[] = [];

  @Output() selectionChange = new EventEmitter<Record<string, unknown>[]>();
  @Output() journalIdClick = new EventEmitter<Record<string, unknown>>();
  @Output() dataGridEvent = new EventEmitter<JournalDataGridEvent>();

  selectedRows = new Set<Record<string, unknown>>();

  get selectedRowsArray(): Record<string, unknown>[] {
    return Array.from(this.selectedRows);
  }

  get hasSelection(): boolean {
    return this.selectedRows.size > 0;
  }

  get isAllSelected(): boolean {
    return this.filteredData.length > 0 && this.selectedRows.size === this.filteredData.length;
  }

  get isIndeterminate(): boolean {
    return (
      this.selectedRows.size > 0 && this.selectedRows.size < this.filteredData.length
    );
  }

  trackByRow(index: number): number {
    return index;
  }

  onRowSelectionChange(row: Record<string, unknown>, selected: boolean) {
    if (selected) {
      this.selectedRows.add(row);
    } else {
      this.selectedRows.delete(row);
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onSelectAllChange(selected: boolean) {
    if (selected) {
      this.filteredData.forEach((row) => this.selectedRows.add(row));
    } else {
      this.selectedRows.clear();
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    this.journalIdClick.emit(row);
    this.emitDataGridEvent('journal-id-click', { row });
  }

  isRowSelected(row: Record<string, unknown>): boolean {
    return this.selectedRows.has(row);
  }

  isSpecialColumn(columnId: string): boolean {
    return ['journalStatus', 'documents', 'journalId'].includes(columnId);
  }

  getCellValue(row: Record<string, unknown>, column: JournalColumn): string {
    const value = row[column.id];
    if (column.render) {
      return column.render(value, row);
    }
    return String(value || '');
  }

  getStatusColor(
    status: string
  ): 'success' | 'warning' | 'info' | 'error' | 'default' {
    switch (status) {
      case 'posted':
        return 'success';
      case 'template':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'ready to approve':
        return 'info';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  }

  getString(value: unknown): string {
    return String(value);
  }

  private emitDataGridEvent(type: JournalDataGridEvent['type'], data?: unknown) {
    const event: JournalDataGridEvent = {
      type,
      data,
      selectedRows: this.selectedRowsArray,
    };
    this.dataGridEvent.emit(event);
  }
}
