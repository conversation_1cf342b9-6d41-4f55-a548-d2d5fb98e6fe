import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { AvaTagComponent } from '../../components/tags/tags.component';

export interface JournalColumn {
  id: string;
  label: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, row: Record<string, unknown>) => string;
}

export interface JournalDataGridEvent {
  type: 'search' | 'filter' | 'sort' | 'create' | 'selection-change' | 'journal-id-click';
  data?: unknown;
  selectedRows?: Record<string, unknown>[];
  searchTerm?: string;
  row?: Record<string, unknown>;
}

@Component({
  selector: 'ava-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    IconComponent,
    CheckboxComponent,
    AvaTextboxComponent,
    AvaTagComponent,
  ],
  templateUrl: './journal-data-grid.component.html',
  styleUrl: './journal-data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class JournalDataGridComponent {
  @Input() data: Record<string, unknown>[] = [];
  @Input() columns: JournalColumn[] = [];
  @Input() loading = false;
  @Input() disabled = false;
  @Input() searchPlaceholder = 'Search for Journal Entries';
  @Input() emptyMessage = 'No journal entries found';
  @Input() loadingMessage = 'Loading journal entries...';

  @Output() searchChange = new EventEmitter<string>();
  @Output() filterClick = new EventEmitter<void>();
  @Output() sortClick = new EventEmitter<void>();
  @Output() createClick = new EventEmitter<void>();
  @Output() selectionChange = new EventEmitter<Record<string, unknown>[]>();
  @Output() journalIdClick = new EventEmitter<Record<string, unknown>>();
  @Output() dataGridEvent = new EventEmitter<JournalDataGridEvent>();

  selectedRows = new Set<Record<string, unknown>>();
  searchTerm = '';

  get selectedRowsArray(): Record<string, unknown>[] {
    return Array.from(this.selectedRows);
  }

  get hasSelection(): boolean {
    return this.selectedRows.size > 0;
  }

  get isAllSelected(): boolean {
    return this.data.length > 0 && this.selectedRows.size === this.data.length;
  }

  get isIndeterminate(): boolean {
    return (
      this.selectedRows.size > 0 && this.selectedRows.size < this.data.length
    );
  }

  get filteredData(): Record<string, unknown>[] {
    let filtered = [...this.data];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchLower)
        )
      );
    }

    return filtered;
  }

  trackByRow(index: number): number {
    return index;
  }

  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const term = target?.value || '';
    this.searchTerm = term;
    this.searchChange.emit(term);
    this.emitDataGridEvent('search', { searchTerm: term });
  }

  onFilterClick() {
    this.filterClick.emit();
    this.emitDataGridEvent('filter');
  }

  onSortClick() {
    this.sortClick.emit();
    this.emitDataGridEvent('sort');
  }

  onCreateClick() {
    this.createClick.emit();
    this.emitDataGridEvent('create');
  }

  onRowSelectionChange(row: Record<string, unknown>, selected: boolean) {
    if (selected) {
      this.selectedRows.add(row);
    } else {
      this.selectedRows.delete(row);
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onSelectAllChange(selected: boolean) {
    if (selected) {
      this.data.forEach((row) => this.selectedRows.add(row));
    } else {
      this.selectedRows.clear();
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    this.journalIdClick.emit(row);
    this.emitDataGridEvent('journal-id-click', { row });
  }

  isRowSelected(row: Record<string, unknown>): boolean {
    return this.selectedRows.has(row);
  }

  isSpecialColumn(columnId: string): boolean {
    return ['journalStatus', 'documents', 'journalId'].includes(columnId);
  }

  getCellValue(row: Record<string, unknown>, column: JournalColumn): string {
    const value = row[column.id];
    if (column.render) {
      return column.render(value, row);
    }
    return String(value || '');
  }

  getStatusColor(
    status: string
  ): 'success' | 'warning' | 'info' | 'error' | 'default' {
    switch (status) {
      case 'posted':
        return 'success';
      case 'template':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'ready to approve':
        return 'info';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  }

  getString(value: unknown): string {
    return String(value);
  }

  private emitDataGridEvent(type: JournalDataGridEvent['type'], data?: unknown) {
    const event: JournalDataGridEvent = {
      type,
      data,
      selectedRows: this.selectedRowsArray,
    };
    this.dataGridEvent.emit(event);
  }
}
